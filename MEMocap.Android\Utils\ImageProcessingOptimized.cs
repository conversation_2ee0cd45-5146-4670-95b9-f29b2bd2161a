using System;
using System.Buffers;

#if ANDROID
using Android.Media;
using AndroidMediaImage = Android.Media.Image;
#endif
namespace MEMocap.Android.Utils
{
    /// <summary>
    /// Optimized image processing utilities for camera frame conversion
    /// </summary>
    public static class ImageProcessingOptimized
    {
        private static readonly ArrayPool<byte> ByteArrayPool = ArrayPool<byte>.Shared;

        /// <summary>
        /// Efficiently converts YUV_420_888 to byte array using buffer pooling
        /// </summary>
#if ANDROID
        public static byte[] ConvertYuv420ToByteArray(AndroidMediaImage image)
        {
            if (image == null) throw new ArgumentNullException(nameof(image));

            var planes = image.GetPlanes();
            if (planes.Length < 3) throw new ArgumentException("Invalid image format");

            var yPlane = planes[0];
            var uPlane = planes[1];
            var vPlane = planes[2];

            int width = image.Width;
            int height = image.Height;
            int ySize = width * height;
            int uvSize = ySize / 4;

            // Use array pooling for better memory management
            byte[] result = new byte[ySize + uvSize * 2];

            try
            {
                // Optimized Y plane copying
                CopyYPlaneOptimized(yPlane, result, 0, width, height);

                // Optimized UV plane copying
                CopyUvPlanesOptimized(uPlane, vPlane, result, ySize, width, height);

                return result;
            }
            catch
            {
                // Return empty array on error rather than throwing
                return Array.Empty<byte>();
            }
        }

        private static void CopyYPlaneOptimized(AndroidMediaImage.Plane yPlane, byte[] destination, int offset, int width, int height)
        {
            var yBuffer = yPlane.Buffer;
            int yStride = yPlane.RowStride;
            int yPixelStride = yPlane.PixelStride;

            if (yPixelStride == 1 && yStride == width)
            {
                // Optimal case: direct copy
                yBuffer.Get(destination, offset, width * height);
            }
            else
            {
                // Row-by-row copy (more efficient than pixel-by-pixel)
                byte[] rowBuffer = ByteArrayPool.Rent(width);
                try
                {
                    for (int row = 0; row < height; row++)
                    {
                        yBuffer.Position(row * yStride);
                        if (yPixelStride == 1)
                        {
                            yBuffer.Get(rowBuffer, 0, width);
                            Array.Copy(rowBuffer, 0, destination, offset + row * width, width);
                        }
                        else
                        {
                            for (int col = 0; col < width; col++)
                            {
                                destination[offset + row * width + col] = 
                                    (byte)(yBuffer.Get(row * yStride + col * yPixelStride) & 0xFF);
                            }
                        }
                    }
                }
                finally
                {
                    ByteArrayPool.Return(rowBuffer);
                }
            }
        }

        private static void CopyUvPlanesOptimized(AndroidMediaImage.Plane uPlane, AndroidMediaImage.Plane vPlane, 
            byte[] destination, int offset, int width, int height)
        {
            var uBuffer = uPlane.Buffer;
            var vBuffer = vPlane.Buffer;
            int uvWidth = width / 2;
            int uvHeight = height / 2;

            int uIndex = offset;
            int vIndex = offset + (width * height) / 4;

            // Optimized UV copying
            for (int row = 0; row < uvHeight; row++)
            {
                for (int col = 0; col < uvWidth; col++)
                {
                    destination[uIndex++] = (byte)(uBuffer.Get() & 0xFF);
                    destination[vIndex++] = (byte)(vBuffer.Get() & 0xFF);
                }
            }
        }

        /// <summary>
        /// Converts YUV420 to NV12 format efficiently
        /// </summary>
        public static byte[] ConvertYuv420ToNv12(AndroidMediaImage image)
        {
            if (image == null) return Array.Empty<byte>();

            var planes = image.GetPlanes();
            var yPlane = planes[0];
            var uPlane = planes[1];
            var vPlane = planes[2];

            int width = image.Width;
            int height = image.Height;
            int ySize = width * height;
            int uvSize = ySize / 4;

            byte[] nv12 = new byte[ySize + uvSize * 2];

            // Copy Y plane
            var yBuffer = yPlane.Buffer;
            yBuffer.Get(nv12, 0, ySize);

            // Interleave U and V for NV12
            var uBuffer = uPlane.Buffer;
            var vBuffer = vPlane.Buffer;
            int uvIndex = ySize;

            for (int i = 0; i < uvSize; i++)
            {
                nv12[uvIndex++] = (byte)(uBuffer.Get() & 0xFF); // U
                nv12[uvIndex++] = (byte)(vBuffer.Get() & 0xFF); // V
            }

            return nv12;
        }
#endif
    }
}
