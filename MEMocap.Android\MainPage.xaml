﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"  
            xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"  
            xmlns:local="clr-namespace:MEMocap.Android"  
            x:Class="MEMocap.Android.MainPage">  

   <Grid>  
       <local:CameraPreview x:Name="CameraPreiewControl" VerticalOptions="FillAndExpand" HorizontalOptions="FillAndExpand" />  
       <Button Text="Start WebRTC" Clicked="OnStartWebRTCClicked"  
               VerticalOptions="End" HorizontalOptions="Center" Margin="20" />  
   </Grid>  

</ContentPage>
